#!/usr/bin/env python3
"""
Main Flask Server for Medical Diagnosis Chat System
Serves both v1 and v2 API endpoints
"""

import os
import logging
from flask import Flask, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# Import blueprints
from routes.v1_routes import v1_bp
from routes.v2_routes import v2_bp

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Enable CORS for frontend integration
    CORS(app)
    
    # Register blueprints
    app.register_blueprint(v1_bp)
    app.register_blueprint(v2_bp)
    
    # Root endpoint
    @app.route('/')
    def root():
        """Root endpoint with API information"""
        return jsonify({
            'message': 'Medical Diagnosis Chat System API',
            'versions': {
                'v1': {
                    'description': 'Original diagnosis API with session management',
                    'endpoints': [
                        'GET /api/v1/health',
                        'POST /api/v1/chat/start',
                        'POST /api/v1/chat/<session_id>/answer',
                        'GET /api/v1/chat/<session_id>/status'
                    ]
                },
                'v2': {
                    'description': 'Enhanced diagnosis API with caching and improved filtering',
                    'endpoints': [
                        'GET /api/v2/health',
                        'POST /api/v2/chat/start',
                        'POST /api/v2/chat/<session_id>/answer',
                        'GET /api/v2/chat/<session_id>/status'
                    ]
                }
            },
            'documentation': {
                'start_chat': {
                    'method': 'POST',
                    'endpoint': '/api/v1/chat/start or /api/v2/chat/start',
                    'body': {
                        'symptoms': 'string - Initial symptoms description'
                    },
                    'response': {
                        'session_id': 'string - Unique session identifier',
                        'type': 'string - Response type (follow_up_question, final_diagnosis, no_matches)',
                        'question': 'string - Follow-up question (if type is follow_up_question)',
                        'diagnosis': 'object - Final diagnosis (if type is final_diagnosis)',
                        'top_diagnoses': 'array - Top matching diagnoses',
                        'version': 'string - API version used'
                    }
                },
                'answer_question': {
                    'method': 'POST',
                    'endpoint': '/api/v1/chat/<session_id>/answer or /api/v2/chat/<session_id>/answer',
                    'body': {
                        'answer': 'string - Answer to the follow-up question'
                    },
                    'response': {
                        'type': 'string - Response type',
                        'question': 'string - Next follow-up question (if applicable)',
                        'diagnosis': 'object - Final diagnosis (if completed)',
                        'top_diagnoses': 'array - Current top matching diagnoses'
                    }
                },
                'get_status': {
                    'method': 'GET',
                    'endpoint': '/api/v1/chat/<session_id>/status or /api/v2/chat/<session_id>/status',
                    'response': {
                        'session_id': 'string - Session identifier',
                        'question_count': 'number - Number of questions asked',
                        'max_questions': 'number - Maximum questions allowed',
                        'is_completed': 'boolean - Whether diagnosis is complete',
                        'symptoms_history': 'array - All symptoms provided',
                        'conversation_history': 'array - Complete conversation log',
                        'current_diagnoses': 'array - Current top diagnoses',
                        'version': 'string - API version'
                    }
                }
            }
        })
    
    # Health check endpoint for the entire service
    @app.route('/health')
    def health():
        """Overall health check endpoint"""
        return jsonify({
            'status': 'healthy',
            'service': 'Medical Diagnosis Chat System',
            'versions': ['v1', 'v2'],
            'endpoints': {
                'v1_health': '/api/v1/health',
                'v2_health': '/api/v2/health'
            }
        })
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors"""
        return jsonify({
            'error': 'Endpoint not found',
            'message': 'Please check the API documentation at the root endpoint /',
            'available_versions': ['v1', 'v2']
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors"""
        logger.error(f"Internal server error: {error}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred. Please try again later.'
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 errors"""
        return jsonify({
            'error': 'Bad request',
            'message': 'Please check your request format and try again.'
        }), 400
    
    return app

def main():
    """Main entry point"""
    app = create_app()
    
    # Get configuration from environment
    port = int(os.getenv('API_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    
    logger.info(f"Starting Medical Diagnosis Chat System API")
    logger.info(f"Available versions: v1, v2")
    logger.info(f"Server running on {host}:{port}")
    logger.info(f"Debug mode: {debug}")
    logger.info(f"API documentation available at: http://{host}:{port}/")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        raise

if __name__ == '__main__':
    main()
