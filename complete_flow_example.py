#!/usr/bin/env python3
"""
Complete example showing the full diagnosis flow with AI scoring and similar diagnosis adjustment
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.v2_routes import ChatSessionV2

def demonstrate_complete_flow():
    """Demonstrate the complete diagnosis flow with AI enhancements"""
    
    print("🏥 Complete AI-Enhanced Diagnosis Flow Demo")
    print("=" * 60)
    
    # Create a test session
    session = ChatSessionV2("demo-session")
    
    # Step 1: Initial symptoms
    initial_symptoms = "headache, fever, and feeling very tired"
    session.symptoms_history = [initial_symptoms]
    session.conversation_history = [
        {"type": "initial_symptoms", "content": initial_symptoms}
    ]
    
    print(f"🩺 Initial Symptoms: {initial_symptoms}")
    
    # Mock initial diagnoses (normally from database)
    initial_diagnoses = [
        {
            "name": "Common Cold",
            "symptoms": "runny nose, nasal congestion, sneezing, mild headache, fatigue",
            "confidence_score": 0.75,
            "diagnosis_id": 1
        },
        {
            "name": "Influenza",
            "symptoms": "fever, headache, muscle aches, fatigue, body aches",
            "confidence_score": 0.70,
            "diagnosis_id": 2
        },
        {
            "name": "Allergic Rhinitis",
            "symptoms": "runny nose, sneezing, nasal congestion, itchy eyes",
            "confidence_score": 0.65,
            "diagnosis_id": 3
        },
        {
            "name": "Migraine",
            "symptoms": "severe headache, nausea, light sensitivity",
            "confidence_score": 0.60,
            "diagnosis_id": 4
        }
    ]
    
    session.cached_diagnoses = initial_diagnoses.copy()
    
    print("\n📊 Initial Diagnosis Scores:")
    for diag in initial_diagnoses:
        print(f"  - {diag['name']}: {diag['confidence_score']:.3f}")
    
    # Step 2: First follow-up question and answer
    print("\n" + "="*60)
    print("🔍 FOLLOW-UP QUESTION 1")
    
    question1 = "Do you have a fever?"
    answer1 = "Yes, I have a high fever of 102°F"
    
    # Add Q&A to conversation history
    session.conversation_history.extend([
        {"type": "question", "content": question1},
        {"type": "answer", "content": answer1}
    ])
    
    print(f"❓ Question: {question1}")
    print(f"💬 Answer: {answer1}")
    
    # Adjust similar diagnoses scores
    try:
        session.cached_diagnoses = session.adjust_similar_diagnoses_scores(
            session.cached_diagnoses, question1, answer1
        )
        
        print("\n📈 Scores after fever confirmation:")
        for diag in session.cached_diagnoses:
            adjustment = diag.get('similarity_adjustment', 0)
            print(f"  - {diag['name']}: {diag['confidence_score']:.3f} (adjustment: {adjustment:+.3f})")
    except Exception as e:
        print(f"❌ Error adjusting scores: {e}")
    
    # Apply AI context filtering
    try:
        ai_filtered = session.filter_diagnoses_by_context(session.cached_diagnoses)
        
        print("\n🤖 AI Context-Filtered Scores:")
        for diag in ai_filtered:
            ai_adj = diag.get('ai_adjustment', 0)
            print(f"  - {diag['name']}: {diag['confidence_score']:.3f} (AI adjustment: {ai_adj:+.3f})")
        
        session.cached_diagnoses = ai_filtered
    except Exception as e:
        print(f"❌ Error in AI filtering: {e}")
    
    # Step 3: Second follow-up question and answer
    print("\n" + "="*60)
    print("🔍 FOLLOW-UP QUESTION 2")
    
    question2 = "Are you experiencing severe muscle aches throughout your body?"
    answer2 = "Yes, my whole body aches terribly"
    
    # Add Q&A to conversation history
    session.conversation_history.extend([
        {"type": "question", "content": question2},
        {"type": "answer", "content": answer2}
    ])
    
    print(f"❓ Question: {question2}")
    print(f"💬 Answer: {answer2}")
    
    # Adjust similar diagnoses scores again
    try:
        session.cached_diagnoses = session.adjust_similar_diagnoses_scores(
            session.cached_diagnoses, question2, answer2
        )
        
        print("\n📈 Scores after muscle aches confirmation:")
        for diag in session.cached_diagnoses:
            adjustment = diag.get('similarity_adjustment', 0)
            print(f"  - {diag['name']}: {diag['confidence_score']:.3f} (adjustment: {adjustment:+.3f})")
    except Exception as e:
        print(f"❌ Error adjusting scores: {e}")
    
    # Apply AI context filtering again
    try:
        final_filtered = session.filter_diagnoses_by_context(session.cached_diagnoses)
        
        print("\n🎯 Final AI-Enhanced Diagnosis Ranking:")
        for i, diag in enumerate(final_filtered, 1):
            ai_adj = diag.get('ai_adjustment', 0)
            sim_adj = diag.get('similarity_adjustment', 0)
            original = diag.get('original_score', 0.0)
            print(f"  {i}. {diag['name']}: {diag['confidence_score']:.3f}")
            print(f"     (Original: {original:.3f}, Similarity: {sim_adj:+.3f}, AI: {ai_adj:+.3f})")
    except Exception as e:
        print(f"❌ Error in final AI filtering: {e}")
    
    print("\n" + "="*60)
    print("🎉 ANALYSIS COMPLETE")
    print("\n💡 Key Features Demonstrated:")
    print("  ✅ AI-powered context scoring")
    print("  ✅ Similar diagnosis score adjustment")
    print("  ✅ Multi-step refinement process")
    print("  ✅ Intelligent medical reasoning")
    print("\n🔬 Expected Outcome:")
    print("  - Influenza should rank highest (fever + muscle aches)")
    print("  - Common Cold should rank lower (no fever typically)")
    print("  - Migraine should rank lower (no fever or muscle aches)")

if __name__ == "__main__":
    demonstrate_complete_flow()
