#!/usr/bin/env python3
"""
V1 REST API routes for Medical Diagnosis Chat System
Provides endpoints for interactive medical diagnosis using vector search
"""

import os
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Blueprint, request, jsonify

from diagnosis_chat import Diagnosis<PERSON><PERSON>, OpenAIEmbedder, DiagnosisDatabase, OpenAILLM
from utils.utils import (
    extract_symptoms_from_question, 
    extract_symptoms_summary, 
    create_enhanced_symptoms_summary,
    convert_to_chat_format,
    extract_keywords_from_question
)

# Configure logging
logger = logging.getLogger(__name__)

# Create Blueprint for v1 routes
v1_bp = Blueprint('v1', __name__, url_prefix='/api/v1')

# In-memory session storage (in production, use Redis or database)
chat_sessions = {}
SESSION_TIMEOUT = timedelta(hours=1)  # Sessions expire after 1 hour

class ChatSession:
    """Represents a chat session with state management"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.symptoms_history = []
        self.previous_questions = []
        self.conversation_history = []  # Track question-answer pairs
        self.excluded_diagnoses = set()
        self.question_count = 0
        self.max_questions = 5
        self.current_diagnoses = []
        self.is_completed = False
        
        # Initialize diagnosis components
        self.embedder = OpenAIEmbedder()
        self.database = DiagnosisDatabase()
        self.llm = OpenAILLM()
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now()
    
    def is_expired(self) -> bool:
        """Check if session has expired"""
        return datetime.now() - self.last_activity > SESSION_TIMEOUT
    
    def add_symptoms(self, symptoms: str):
        """Add symptoms to history"""
        self.symptoms_history.append(symptoms)
        self.update_activity()
    
    def add_question(self, question: str):
        """Add question to history"""
        self.previous_questions.append(question)
        self.update_activity()
    
    def get_combined_symptoms(self) -> str:
        """Get all symptoms combined"""
        return " ".join(self.symptoms_history)

def cleanup_expired_sessions():
    """Remove expired sessions from memory"""
    expired_sessions = [
        session_id for session_id, session in chat_sessions.items()
        if session.is_expired()
    ]
    for session_id in expired_sessions:
        del chat_sessions[session_id]
    
    if expired_sessions:
        logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")

def get_or_create_session(session_id: str = None) -> ChatSession:
    """Get existing session or create new one"""
    cleanup_expired_sessions()
    
    if session_id and session_id in chat_sessions:
        session = chat_sessions[session_id]
        session.update_activity()
        return session
    
    # Create new session
    new_session_id = session_id or str(uuid.uuid4())
    session = ChatSession(new_session_id)
    chat_sessions[new_session_id] = session
    logger.info(f"Created new chat session: {new_session_id}")
    return session

@v1_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        database = DiagnosisDatabase()
        diagnosis_count = database.get_diagnosis_count()
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database_diagnoses': diagnosis_count,
            'active_sessions': len(chat_sessions),
            'version': 'v1'
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'version': 'v1'
        }), 500

@v1_bp.route('/chat/start', methods=['POST'])
def start_chat():
    """Start a new chat session"""
    try:
        data = request.get_json() or {}
        initial_symptoms = data.get('symptoms', '').strip()
        
        if not initial_symptoms:
            return jsonify({
                'error': 'Initial symptoms are required'
            }), 400
        
        # Create new session
        session = get_or_create_session()
        session.add_symptoms(initial_symptoms)

        # Add initial symptoms to conversation history
        session.conversation_history.append({
            "step": 1,
            "type": "initial_symptoms",
            "content": initial_symptoms
        })
        
        # Process initial symptoms
        result = process_symptoms_for_session(session)
        
        response = {
            'session_id': session.session_id,
            'message': 'Chat session started successfully',
            'version': 'v1',
            **result
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error starting chat: {e}")
        return jsonify({
            'error': 'Failed to start chat session',
            'version': 'v1'
        }), 500

@v1_bp.route('/chat/<session_id>/answer', methods=['POST'])
def answer_question(session_id: str):
    """Answer a follow-up question"""
    try:
        if session_id not in chat_sessions:
            return jsonify({
                'error': 'Session not found or expired'
            }), 404
        
        session = chat_sessions[session_id]
        data = request.get_json() or {}
        answer = data.get('answer', '').strip()
        
        if not answer:
            return jsonify({
                'error': 'Answer is required'
            }), 400
        
        # Add answer to symptoms history
        session.add_symptoms(answer)

        # Add answer to conversation history
        session.conversation_history.append({
            "step": len(session.conversation_history) + 1,
            "type": "answer",
            "content": answer,
            "question_number": session.question_count
        })

        # Analyze response and filter diagnoses
        if session.current_diagnoses and session.previous_questions:
            last_question = session.previous_questions[-1]
            analyze_response_and_filter(session, answer, session.current_diagnoses, last_question)
        
        # Process symptoms with new information
        result = process_symptoms_for_session(session)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing answer: {e}")
        return jsonify({
            'error': 'Failed to process answer'
        }), 500

@v1_bp.route('/chat/<session_id>/status', methods=['GET'])
def get_chat_status(session_id: str):
    """Get current chat session status"""
    try:
        if session_id not in chat_sessions:
            return jsonify({
                'error': 'Session not found or expired'
            }), 404
        
        session = chat_sessions[session_id]
        
        return jsonify({
            'session_id': session.session_id,
            'question_count': session.question_count,
            'max_questions': session.max_questions,
            'is_completed': session.is_completed,
            'symptoms_history': session.symptoms_history,
            'previous_questions': session.previous_questions,
            'conversation_history': session.conversation_history,
            'current_diagnoses': session.current_diagnoses[:5] if session.current_diagnoses else [],
            'version': 'v1'
        })
        
    except Exception as e:
        logger.error(f"Error getting chat status: {e}")
        return jsonify({
            'error': 'Failed to get chat status'
        }), 500

def process_symptoms_for_session(session: ChatSession) -> Dict:
    """Process symptoms for a session and return result"""
    try:
        # Get combined symptoms (keep for backward compatibility)
        combined_symptoms = session.get_combined_symptoms()

        # Get enhanced symptoms summary that includes inferred symptoms from positive answers
        enhanced_symptoms = create_enhanced_symptoms_summary(session.conversation_history)

        # Also get the old symptoms summary for comparison
        symptoms_summary = extract_symptoms_summary(session.conversation_history)

        # Log the different approaches for debugging
        logger.info(f"Combined symptoms: {combined_symptoms}")
        logger.info(f"Old symptoms summary: {symptoms_summary}")
        logger.info(f"Enhanced symptoms summary: {enhanced_symptoms}")

        # Get embedding for enhanced symptoms
        embeddings = session.embedder.get_embeddings([enhanced_symptoms])
        if not embeddings:
            return {
                'error': 'Failed to process symptoms',
                'type': 'processing_error'
            }
        
        # Search for similar diagnoses
        diagnoses = session.database.search_similar_diagnoses(embeddings[0], top_k=10)
        
        # Filter out excluded diagnoses and low confidence ones
        diagnoses = [d for d in diagnoses if d['name'] not in session.excluded_diagnoses]
        diagnoses = [d for d in diagnoses if d['confidence_score'] >= 0.4]
        
        if not diagnoses:
            return {
                'type': 'no_matches',
                'message': 'No matching diagnoses found. Please consult a medical professional.',
                'is_completed': True
            }
        
        session.current_diagnoses = diagnoses
        top_confidence = diagnoses[0]['confidence_score']
        
        # Check if we should provide final diagnosis
        if session.question_count >= session.max_questions or top_confidence > 0.8:
            session.is_completed = True
            return {
                'type': 'final_diagnosis',
                'diagnosis': diagnoses[0],
                'top_diagnoses': diagnoses[:5],
                'is_completed': True,
                'confidence_threshold_met': top_confidence > 0.8,
                'max_questions_reached': session.question_count >= session.max_questions
            }
        
        # Convert conversation history to chat format for LLM
        chat_format_history = convert_to_chat_format(session.conversation_history)

        # Debug logging
        logger.info(f"Session conversation history length: {len(session.conversation_history)}")
        logger.info(f"Chat format history length: {len(chat_format_history)}")
        if session.conversation_history:
            logger.info(f"Last conversation entry: {session.conversation_history[-1]}")

        # Try to generate follow-up question using enhanced symptoms summary
        question = session.llm.generate_follow_up_question(
            enhanced_symptoms, diagnoses, session.previous_questions, chat_format_history
        )
        
        if question is None:
            # No good question available, provide final diagnosis
            session.is_completed = True
            return {
                'type': 'final_diagnosis',
                'diagnosis': diagnoses[0],
                'top_diagnoses': diagnoses[:5],
                'is_completed': True,
                'reason': 'No further differentiating questions available'
            }
        
        # Increment question count and store question
        session.question_count += 1
        session.add_question(question)

        # Add question to conversation history
        session.conversation_history.append({
            "step": len(session.conversation_history) + 1,
            "type": "question",
            "content": question,
            "question_number": session.question_count
        })
        
        return {
            'type': 'follow_up_question',
            'question': question,
            'question_number': session.question_count,
            'max_questions': session.max_questions,
            'top_diagnoses': diagnoses[:5],
            'is_completed': False
        }
        
    except Exception as e:
        logger.error(f"Error processing symptoms: {e}")
        return {
            'error': 'Failed to process symptoms',
            'type': 'processing_error'
        }

def analyze_response_and_filter(session: ChatSession, response: str, diagnoses: List[Dict], question: str):
    """Analyze user response and filter out irrelevant diagnoses"""
    response_lower = response.lower()
    question_lower = question.lower()
    
    # Simple keyword-based filtering
    negative_responses = ['no', 'not', 'none', 'never', 'absent', 'negative']
    positive_responses = ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']
    
    is_negative = any(neg in response_lower for neg in negative_responses)
    is_positive = any(pos in response_lower for pos in positive_responses)
    
    # If user gives a clear negative response, exclude relevant diagnoses
    if is_negative and not is_positive:
        # Special case: if question is about udder/milk and user says no, exclude all mastitis
        if any(term in question_lower for term in ['udder', 'milk', 'mammary']):
            mastitis_diagnoses = [
                'Acute Mastitis', 'Sub Acute Mastitis', 'Mild Mastitis',
                'Gangrenous Mastitis', 'Per Acute Mastitis', 'Chronic Mastitis',
                'Clinical Mastitis', 'Subclinical Mastitis'
            ]
            
            for mastitis_name in mastitis_diagnoses:
                session.excluded_diagnoses.add(mastitis_name)
            
            # Also check for any diagnosis containing mastitis keywords
            mastitis_patterns = ['mastitis', 'mammary', 'udder']
            for diagnosis in diagnoses:
                diagnosis_name_lower = diagnosis['name'].lower()
                if any(keyword in diagnosis_name_lower for keyword in mastitis_patterns):
                    session.excluded_diagnoses.add(diagnosis['name'])
        
        # General keyword-based exclusion
        else:
            question_keywords = extract_keywords_from_question(question)
            
            for diagnosis in diagnoses[:5]:  # Check top 5 diagnoses
                diagnosis_symptoms = diagnosis['symptoms'].lower()
                diagnosis_name = diagnosis['name'].lower()
                # If the diagnosis heavily features the questioned symptom and user says no, exclude it
                keyword_matches = sum(1 for keyword in question_keywords
                                    if keyword in diagnosis_symptoms or keyword in diagnosis_name)
                if keyword_matches >= 1:  # If any keyword matches
                    session.excluded_diagnoses.add(diagnosis['name'])
