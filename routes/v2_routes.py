#!/usr/bin/env python3
"""
V2 REST API routes for Medical Diagnosis Chat System
Enhanced version with improved caching and filtering
"""

import os
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, List
from flask import Blueprint, request, jsonify
import openai

from diagnosis_chat import OpenAIEmbedder, OpenAILLM
from utils.utils import convert_to_chat_format

# Configure logging
logger = logging.getLogger(__name__)

# Create Blueprint for v2 routes
v2_bp = Blueprint('v2', __name__, url_prefix='/api/v2')

# In-memory session storage (in production, use Redis or database)
chat_sessions_v2 = {}
SESSION_TIMEOUT = timedelta(hours=1)  # Sessions expire after 1 hour

# Import the v2 database class
import psycopg2

class DiagnosisDatabaseV2:
    """Database manager for diagnosis vector search"""

    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")
        
        if self.database_url:
            self.db_config = None
        else:
            self.db_config = {
                "dbname": os.getenv("DB_NAME", "kforce"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASS", "kforce"),
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5444")
            }

    def get_connection(self):
        """Get database connection"""
        if self.database_url:
            return psycopg2.connect(self.database_url)
        else:
            return psycopg2.connect(**self.db_config)

    def search_similar_diagnoses(self, query_embedding: List[float], top_k: int = 5) -> List[Dict]:
        """Search for similar diagnoses using vector similarity"""
        
        search_sql = """
        SELECT
            rd.name,
            d.symptoms,
            CASE
                WHEN symptoms_embedding IS NULL THEN 0.0
                WHEN %s::vector IS NULL THEN 0.0
                ELSE GREATEST(0.0, LEAST(1.0, 1 - (symptoms_embedding <=> %s::vector)))
            END as confidence_score,
            diagnosis_id
        FROM config.diagnoses_embeddings as d
        LEFT JOIN main.ref_diagnosis as rd ON d.diagnosis_id = rd.reference_id
        WHERE d.symptoms_embedding IS NOT NULL
        ORDER BY d.symptoms_embedding <=> %s::vector
        LIMIT %s
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(search_sql, (query_embedding, query_embedding, query_embedding, top_k))
                    results = cur.fetchall()

                    diagnoses = []
                    for row in results:
                        diagnoses.append({
                            "name": row[0],
                            "symptoms": row[1],
                            "confidence_score": float(row[2]),
                            "diagnosis_id": row[3]
                        })
                    print('diagnosis',diagnoses)
                    return diagnoses

        except Exception:
            return []

    def get_diagnosis_count(self) -> int:
        """Get total number of diagnoses in database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT COUNT(*) FROM config.diagnoses_embeddings")
                    return cur.fetchone()[0]
        except Exception:
            return 0

class ChatSessionV2:
    """Enhanced chat session with caching and improved filtering"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.symptoms_history = []
        self.previous_questions = []
        self.conversation_history = []
        self.excluded_diagnoses = set()
        self.question_count = 0
        self.max_questions = 5
        self.current_diagnoses = []
        self.is_completed = False
        
        # Initialize diagnosis components
        self.embedder = OpenAIEmbedder()
        self.database = DiagnosisDatabaseV2()
        self.llm = OpenAILLM()
        
        # Cache for embeddings to avoid regenerating
        self.cached_embedding = None
        self.cached_diagnoses = None
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now()
    
    def is_expired(self) -> bool:
        """Check if session has expired"""
        return datetime.now() - self.last_activity > SESSION_TIMEOUT
    
    def add_symptoms(self, symptoms: str):
        """Add symptoms to history"""
        self.symptoms_history.append(symptoms)
        self.update_activity()
    
    def add_question(self, question: str):
        """Add question to history"""
        self.previous_questions.append(question)
        self.update_activity()
    
    def get_combined_symptoms(self) -> str:
        """Get all symptoms combined"""
        return " ".join(self.symptoms_history)
    
    def generate_initial_embedding_and_diagnoses(self):
        """Generate embedding and fetch diagnoses only once"""
        if not self.symptoms_history:
            return
            
        initial_symptoms = self.symptoms_history[0]  # Use only the first (main) symptoms
        logger.info(f"Generating embedding for: {initial_symptoms}")
        
        embeddings = self.embedder.get_embeddings([initial_symptoms])
        if embeddings:
            self.cached_embedding = embeddings[0]
            logger.info("Embedding cached successfully")
            
            # Now fetch and cache diagnoses - ONLY ONCE
            logger.info("Fetching diagnoses from database...")
            diagnoses = self.database.search_similar_diagnoses(self.cached_embedding, top_k=10)
            
            # Filter by confidence threshold and cache
            self.cached_diagnoses = [d for d in diagnoses if d['confidence_score'] >= 0.3]
            logger.info(f"{len(self.cached_diagnoses)} diagnoses cached successfully")
        else:
            logger.error("Error generating embedding")

    def get_cached_diagnoses(self):
        """Return cached diagnoses without any database query"""
        if self.cached_diagnoses is None:
            logger.error("No cached diagnoses available")
            return []
        
        # Return a copy to avoid modifying the original cached data
        return self.cached_diagnoses.copy()

    def filter_diagnoses_by_context(self, diagnoses):
        """AI-powered filtering of diagnoses based on conversation context"""

        if not self.conversation_history or len(self.conversation_history) <= 1:
            return diagnoses  # No filtering for initial symptoms

        # Use AI to score diagnoses based on conversation context
        ai_scored_diagnoses = self._ai_score_diagnoses(diagnoses)

        # Sort by AI-generated confidence score
        ai_scored_diagnoses.sort(key=lambda x: x['confidence_score'], reverse=True)

        # Return diagnoses with meaningful scores (filter out very low scores)
        return [d for d in ai_scored_diagnoses if d['confidence_score'] > 0.3]

    def _ai_score_diagnoses(self, diagnoses):
        """Use AI to score diagnoses based on conversation context"""
        try:
            # Prepare conversation context for AI analysis
            conversation_context = self._prepare_conversation_context()

            scored_diagnoses = []

            for diagnosis in diagnoses:
                # Get AI score for this specific diagnosis
                ai_score = self._get_ai_diagnosis_score(diagnosis, conversation_context)

                scored_diagnoses.append({
                    **diagnosis,
                    'confidence_score': ai_score,
                    'original_score': diagnosis['confidence_score'],
                    'ai_adjustment': ai_score - diagnosis['confidence_score']
                })

            return scored_diagnoses

        except Exception as e:
            logger.error(f"Error in AI scoring: {e}")
            # Fallback to original scores if AI fails
            return diagnoses

    def _prepare_conversation_context(self):
        """Prepare conversation history for AI analysis"""
        context_parts = []

        # Add initial symptoms
        if self.symptoms_history:
            context_parts.append(f"Initial symptoms: {self.symptoms_history[0]}")

        # Add Q&A pairs
        current_question = None
        for entry in self.conversation_history:
            if entry.get('type') == 'question':
                current_question = entry.get('content', '')
            elif entry.get('type') == 'answer' and current_question:
                context_parts.append(f"Q: {current_question}")
                context_parts.append(f"A: {entry.get('content', '')}")
                current_question = None

        return "\n".join(context_parts)

    def adjust_similar_diagnoses_scores(self, diagnoses, last_question, last_answer):
        """Adjust confidence scores of similar diagnoses based on the latest Q&A"""
        try:
            # Use AI to determine which diagnoses should have their scores adjusted
            adjusted_diagnoses = self._ai_adjust_similar_diagnoses(diagnoses, last_question, last_answer)
            return adjusted_diagnoses
        except Exception as e:
            logger.error(f"Error adjusting similar diagnoses scores: {e}")
            return diagnoses

    def _ai_adjust_similar_diagnoses(self, diagnoses, question, answer):
        """Use AI to adjust scores of similar diagnoses based on Q&A"""
        try:
            # Create context for AI analysis
            diagnoses_context = "\n".join([
                f"- {d['name']} (current score: {d['confidence_score']:.3f}): {d['symptoms']}"
                for d in diagnoses
            ])

            prompt = f"""You are a medical AI assistant. Based on the patient's answer to a follow-up question, adjust the confidence scores for similar diagnoses.

Question asked: {question}
Patient's answer: {answer}

Current diagnoses and scores:
{diagnoses_context}

Instructions:
1. Analyze how the patient's answer affects each diagnosis
2. If the answer supports a diagnosis, increase its score
3. If the answer contradicts a diagnosis, decrease its score
4. If the answer is neutral/irrelevant, keep the score unchanged
5. Provide new scores between 0.0 and 1.0

Respond with ONLY a JSON object in this format:
{{"diagnosis_name_1": new_score, "diagnosis_name_2": new_score, ...}}

Example: {{"Common Cold": 0.85, "Influenza": 0.45, "Allergic Rhinitis": 0.30}}"""

            # Use the existing OpenAI LLM to get adjusted scores
            import openai
            response = openai.chat.completions.create(
                model=self.llm.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.1
            )

            if response and response.choices:
                # Parse the JSON response
                response_text = response.choices[0].message.content.strip()
                try:
                    import json
                    score_adjustments = json.loads(response_text)

                    # Apply the adjustments
                    adjusted_diagnoses = []
                    for diagnosis in diagnoses:
                        new_score = score_adjustments.get(diagnosis['name'], diagnosis['confidence_score'])
                        # Ensure score is within valid range
                        new_score = max(0.0, min(1.0, float(new_score)))

                        adjusted_diagnoses.append({
                            **diagnosis,
                            'confidence_score': new_score,
                            'original_score': diagnosis.get('original_score', diagnosis['confidence_score']),
                            'similarity_adjustment': new_score - diagnosis['confidence_score']
                        })

                    return adjusted_diagnoses

                except (json.JSONDecodeError, ValueError) as e:
                    logger.warning(f"Failed to parse AI score adjustments: {e}")
                    return diagnoses
            else:
                return diagnoses

        except Exception as e:
            logger.error(f"Error in AI similarity adjustment: {e}")
            return diagnoses

    def _get_ai_diagnosis_score(self, diagnosis, conversation_context):
        """Get AI-generated score for a specific diagnosis"""
        try:
            prompt = f"""You are a medical AI assistant. Based on the patient's conversation history, provide a confidence score (0.0 to 1.0) for how well this diagnosis matches the patient's symptoms and responses.

Conversation History:
{conversation_context}

Diagnosis to evaluate:
- Name: {diagnosis['name']}
- Symptoms: {diagnosis['symptoms']}
- Original confidence: {diagnosis['confidence_score']:.3f}

Instructions:
1. Analyze how well the patient's answers align with this diagnosis
2. Consider both positive and negative indicators from their responses
3. Factor in the original confidence score as a baseline
4. Provide a final confidence score between 0.0 and 1.0

Respond with ONLY a decimal number between 0.0 and 1.0 (e.g., 0.75)"""

            # Use the existing OpenAI LLM to get the score
            import openai
            response = openai.chat.completions.create(
                model=self.llm.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )

            if response and response.choices:
                # Extract numeric score from response
                score_text = response.choices[0].message.content.strip()
                try:
                    ai_score = float(score_text)
                    # Ensure score is within valid range
                    ai_score = max(0.0, min(1.0, ai_score))
                    return ai_score
                except ValueError:
                    logger.warning(f"Invalid AI score format: {score_text}")
                    return diagnosis['confidence_score']
            else:
                return diagnosis['confidence_score']

        except Exception as e:
            logger.error(f"Error getting AI diagnosis score: {e}")
            return diagnosis['confidence_score']

def cleanup_expired_sessions_v2():
    """Remove expired sessions from memory"""
    expired_sessions = [
        session_id for session_id, session in chat_sessions_v2.items()
        if session.is_expired()
    ]
    for session_id in expired_sessions:
        del chat_sessions_v2[session_id]
    
    if expired_sessions:
        logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")

def get_or_create_session_v2(session_id: str = None) -> ChatSessionV2:
    """Get existing session or create new one"""
    cleanup_expired_sessions_v2()
    
    if session_id and session_id in chat_sessions_v2:
        session = chat_sessions_v2[session_id]
        session.update_activity()
        return session
    
    # Create new session
    new_session_id = session_id or str(uuid.uuid4())
    session = ChatSessionV2(new_session_id)
    chat_sessions_v2[new_session_id] = session
    logger.info(f"Created new chat session v2: {new_session_id}")
    return session

@v2_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        database = DiagnosisDatabaseV2()
        diagnosis_count = database.get_diagnosis_count()
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database_diagnoses': diagnosis_count,
            'active_sessions': len(chat_sessions_v2),
            'version': 'v2'
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'version': 'v2'
        }), 500

@v2_bp.route('/chat/start', methods=['POST'])
def start_chat():
    """Start a new chat session"""
    try:
        data = request.get_json() or {}
        initial_symptoms = data.get('symptoms', '').strip()
        
        if not initial_symptoms:
            return jsonify({
                'error': 'Initial symptoms are required'
            }), 400
        
        # Create new session
        session = get_or_create_session_v2()
        session.add_symptoms(initial_symptoms)

        # Add initial symptoms to conversation history
        session.conversation_history.append({
            "step": 1,
            "type": "initial_symptoms",
            "content": initial_symptoms
        })
        
        # Generate initial embedding and cache diagnoses
        session.generate_initial_embedding_and_diagnoses()
        
        # Process initial symptoms
        result = process_symptoms_for_session_v2(session)
        
        response = {
            'session_id': session.session_id,
            'message': 'Chat session started successfully',
            'version': 'v2',
            **result
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error starting chat: {e}")
        return jsonify({
            'error': 'Failed to start chat session',
            'version': 'v2'
        }), 500



@v2_bp.route('/chat/<session_id>/status', methods=['GET'])
def get_chat_status(session_id: str):
    """Get current chat session status"""
    try:
        if session_id not in chat_sessions_v2:
            return jsonify({
                'error': 'Session not found or expired'
            }), 404

        session = chat_sessions_v2[session_id]

        return jsonify({
            'session_id': session.session_id,
            'question_count': session.question_count,
            'max_questions': session.max_questions,
            'is_completed': session.is_completed,
            'symptoms_history': session.symptoms_history,
            'previous_questions': session.previous_questions,
            'conversation_history': session.conversation_history,
            'current_diagnoses': session.current_diagnoses[:5] if session.current_diagnoses else [],
            'version': 'v2',
            'cached_diagnoses_count': len(session.cached_diagnoses) if session.cached_diagnoses else 0
        })

    except Exception as e:
        logger.error(f"Error getting chat status: {e}")
        return jsonify({
            'error': 'Failed to get chat status'
        }), 500

def process_symptoms_for_session_v2(session: ChatSessionV2) -> Dict:
    """Process symptoms for a session and return result (v2 with caching)"""
    try:
        # Use cached diagnoses - NO database queries after initial load
        if session.cached_diagnoses is None:
            return {
                'error': 'No cached diagnoses available. Please restart the session.',
                'type': 'processing_error'
            }

        # Get cached diagnoses (no DB query)
        diagnoses = session.get_cached_diagnoses()

        if not diagnoses:
            return {
                'type': 'no_matches',
                'message': 'No matching diagnoses found. Please consult a medical professional.',
                'is_completed': True
            }

        # Apply intelligent filtering based on conversation context
        filtered_diagnoses = session.filter_diagnoses_by_context(diagnoses)

        if not filtered_diagnoses:
            return {
                'type': 'no_matches',
                'message': 'No matching diagnoses found after filtering. Please consult a medical professional.',
                'is_completed': True
            }

        session.current_diagnoses = filtered_diagnoses
        top_confidence = filtered_diagnoses[0]['confidence_score']

        # Provide final diagnosis (no follow-up questions)
        session.is_completed = True
        return {
            'type': 'final_diagnosis',
            'diagnosis': filtered_diagnoses[0],
            'top_diagnoses': filtered_diagnoses[:5],
            'is_completed': True,
            'confidence_score': top_confidence
        }

    except Exception as e:
        logger.error(f"Error processing symptoms: {e}")
        return {
            'error': 'Failed to process symptoms',
            'type': 'processing_error'
        }
