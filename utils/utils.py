#!/usr/bin/env python3
"""
Utility functions shared between v1 and v2 diagnosis systems
"""

import re
import uuid
from datetime import datetime
from typing import Dict, List


def extract_symptoms_from_question(question: str) -> List[str]:
    """Extract specific symptoms mentioned in a follow-up question"""
    question_lower = question.lower()
    symptoms = []

    # Common patterns for symptoms in questions
    symptom_patterns = [
        r'such as ([^?]+)',  # "such as cough or difficulty breathing"
        r'including ([^?]+)',  # "including fever or chills"
        r'like ([^?]+)',  # "like vomiting or diarrhea"
        r'any ([^,?]+)',  # "any pain or discomfort"
    ]

    for pattern in symptom_patterns:
        matches = re.findall(pattern, question_lower)
        for match in matches:
            # Split by 'or', 'and', commas and clean up
            symptom_parts = re.split(r'\s+or\s+|\s+and\s+|,', match)
            for part in symptom_parts:
                clean_symptom = part.strip()
                if clean_symptom and len(clean_symptom) > 2:
                    symptoms.append(clean_symptom)

    # Also look for direct symptom mentions
    direct_symptoms = [
        'cough', 'fever', 'breathing', 'respiratory', 'temperature',
        'pain', 'swelling', 'discharge', 'inflammation', 'lethargy',
        'weakness', 'diarrhea', 'vomiting', 'appetite', 'eating',
        'drinking', 'lameness', 'walking', 'movement'
    ]

    for symptom in direct_symptoms:
        if symptom in question_lower and symptom not in symptoms:
            symptoms.append(symptom)

    return symptoms


def extract_symptoms_summary(conversation_history: List[Dict]) -> str:
    """Extract a clean summary of current symptoms from conversation history"""
    if not conversation_history:
        return "No symptoms provided"

    # Get initial symptoms and additional info from chat format or simple format
    initial_symptoms = ""
    additional_info = []

    for step in conversation_history:
        # Handle both chat format and simple format
        if isinstance(step, dict):
            # Chat format (with role, content, parts)
            if step.get('role') == 'user':
                content = step.get('content', '')
                # First user message is initial symptoms
                if not initial_symptoms and content:
                    initial_symptoms = content
                elif content and not content.lower().startswith('no'):
                    additional_info.append(content)
            # Simple format (with type)
            elif step.get('type') == 'initial_symptoms':
                initial_symptoms = step.get('content', '')
            elif step.get('type') == 'answer':
                answer = step.get('content', '').strip()
                if answer and not answer.lower().startswith('no'):
                    additional_info.append(answer)

    # Combine initial symptoms with relevant additional information
    if additional_info:
        return f"{initial_symptoms}. Additional findings: {'; '.join(additional_info)}"
    else:
        return initial_symptoms


def create_enhanced_symptoms_summary(conversation_history: List[Dict]) -> str:
    """Create an enhanced symptoms summary that includes inferred symptoms from positive answers"""
    if not conversation_history:
        return "No symptoms provided"

    # Start with initial symptoms
    initial_symptoms = ""
    enhanced_symptoms = []
    current_question = ""

    for entry in conversation_history:
        if entry.get('type') == 'initial_symptoms':
            initial_symptoms = entry.get('content', '')
            enhanced_symptoms.append(initial_symptoms)
        elif entry.get('role') == 'user' and not initial_symptoms:
            # Handle chat format - first user message is initial symptoms
            initial_symptoms = entry.get('content', '')
            enhanced_symptoms.append(initial_symptoms)
        elif entry.get('type') == 'question':
            # Store the question for the next answer
            current_question = entry.get('content', '')
        elif entry.get('role') == 'assistant':
            # Handle chat format - assistant questions
            current_question = entry.get('content', '')
        elif entry.get('type') == 'answer':
            answer = entry.get('content', '').strip().lower()

            # If user gave a positive answer, extract symptoms from the previous question
            if any(positive in answer for positive in ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']):
                if current_question:
                    extracted_symptoms = extract_symptoms_from_question(current_question)
                    enhanced_symptoms.extend(extracted_symptoms)
            # If user gave additional descriptive information (not just yes/no)
            elif answer not in ['yes', 'no', 'none', 'never', 'not', 'negative', 'absent']:
                enhanced_symptoms.append(answer)
        elif entry.get('role') == 'user' and initial_symptoms:
            # Handle chat format - subsequent user messages are answers
            answer = entry.get('content', '').strip().lower()

            # If user gave a positive answer, extract symptoms from the previous question
            if any(positive in answer for positive in ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']):
                if current_question:
                    extracted_symptoms = extract_symptoms_from_question(current_question)
                    enhanced_symptoms.extend(extracted_symptoms)
            # If user gave additional descriptive information (not just yes/no)
            elif answer not in ['yes', 'no', 'none', 'never', 'not', 'negative', 'absent']:
                enhanced_symptoms.append(answer)

    # Remove duplicates while preserving order
    seen = set()
    unique_symptoms = []
    for symptom in enhanced_symptoms:
        if symptom and symptom.lower() not in seen:
            seen.add(symptom.lower())
            unique_symptoms.append(symptom)

    return " ".join(unique_symptoms)


def convert_to_chat_format(conversation_history: List[Dict]) -> List[Dict]:
    """Convert conversation history to chat format similar to the provided example"""
    if not conversation_history:
        return []

    chat_format = []
    for step in conversation_history:
        # Check if already in chat format (has 'role' field)
        if step.get('role'):
            # Already in chat format, just return as-is
            chat_format.append(step)
        # Convert from simple format (has 'type' field)
        elif step.get('type') == 'initial_symptoms':
            chat_format.append({
                "role": "user",
                "content": step.get('content', ''),
                "id": str(uuid.uuid4()),
                "createdAt": datetime.now().isoformat() + "Z",
                "parts": [
                    {
                        "type": "text",
                        "text": step.get('content', '')
                    }
                ]
            })
        elif step.get('type') == 'question':
            chat_format.append({
                "role": "assistant",
                "content": step.get('content', ''),
                "id": str(uuid.uuid4()),
                "createdAt": datetime.now().isoformat() + "Z",
                "parts": [
                    {
                        "type": "step-start"
                    },
                    {
                        "type": "text",
                        "text": step.get('content', '')
                    }
                ]
            })
        elif step.get('type') == 'answer':
            chat_format.append({
                "role": "user",
                "content": step.get('content', ''),
                "id": str(uuid.uuid4()),
                "createdAt": datetime.now().isoformat() + "Z",
                "parts": [
                    {
                        "type": "text",
                        "text": step.get('content', '')
                    }
                ]
            })

    return chat_format


def extract_keywords_from_question(question: str) -> List[str]:
    """Extract key medical terms from a question"""
    medical_keywords = [
        'respiratory', 'breathing', 'cough', 'fever', 'temperature',
        'udder', 'milk', 'swollen', 'inflammation', 'pain', 'discharge',
        'appetite', 'eating', 'drinking', 'lethargy', 'weakness',
        'diarrhea', 'vomiting', 'skin', 'lesions', 'wounds',
        'lameness', 'walking', 'movement', 'joints', 'legs'
    ]
    
    question_lower = question.lower()
    found_keywords = []
    
    for keyword in medical_keywords:
        if keyword in question_lower:
            found_keywords.append(keyword)
    
    return found_keywords


def extract_keywords_from_question_v2(question: str) -> List[str]:
    """Extract relevant medical keywords from a question (v2 implementation)"""
    # Remove common question words
    question_clean = re.sub(r'\b(do|you|have|any|are|there|is|was|were|been|being|experience|experiencing|feel|feeling)\b', '', question, flags=re.IGNORECASE)
    
    # Extract potential symptom words (longer than 3 characters)
    words = re.findall(r'\b[a-zA-Z]{4,}\b', question_clean)
    
    # Filter out common non-medical words
    common_words = {'with', 'that', 'this', 'they', 'them', 'when', 'where', 'what', 'which', 'would', 'could', 'should', 'might', 'such', 'like', 'also', 'even', 'just', 'only', 'more', 'most', 'much', 'many', 'some', 'time', 'times', 'often', 'usually', 'sometimes', 'always', 'never'}
    
    medical_keywords = [word.lower() for word in words if word.lower() not in common_words]
    
    return medical_keywords
