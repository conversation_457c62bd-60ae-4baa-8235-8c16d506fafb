# Similar Diagnosis Score Adjustment Feature

## Overview

The similar diagnosis score adjustment feature enhances the follow-up question functionality by intelligently adjusting confidence scores of related diagnoses based on patient responses. This ensures that when a patient confirms or denies specific symptoms, all similar diagnoses are appropriately re-scored.

## Problem Solved

**Before**: Follow-up questions were generated, but the confidence scores of similar diagnoses weren't updated based on the patient's specific answers.

**After**: When a patient answers a follow-up question, the system uses AI to analyze how that answer affects all similar diagnoses and adjusts their confidence scores accordingly.

## Implementation

### New Methods Added

1. **`adjust_similar_diagnoses_scores(self, diagnoses, last_question, last_answer)`**
   - Main entry point for adjusting scores based on Q&A
   - Calls AI-powered adjustment logic
   - Handles errors gracefully

2. **`_ai_adjust_similar_diagnoses(self, diagnoses, question, answer)`**
   - Uses OpenAI to analyze how the answer affects each diagnosis
   - Returns JSON with new confidence scores
   - Applies intelligent medical reasoning

### Integration Points

The feature is integrated into the answer processing flow in `routes/v2_routes.py`:

```python
# In answer_question endpoint
if last_question and session.cached_diagnoses:
    session.cached_diagnoses = session.adjust_similar_diagnoses_scores(
        session.cached_diagnoses, last_question, answer
    )
```

## How It Works

### Step 1: Question Analysis
When a follow-up question is answered, the system:
1. Captures the question and answer
2. Analyzes all current diagnoses
3. Determines how the answer affects each diagnosis

### Step 2: AI-Powered Scoring
The AI receives:
- The specific question asked
- The patient's answer
- Current diagnoses with their symptoms and scores

The AI analyzes:
- Whether the answer supports each diagnosis
- Whether the answer contradicts each diagnosis
- Medical relationships between symptoms and conditions

### Step 3: Score Adjustment
The system:
- Applies new confidence scores (0.0 to 1.0)
- Tracks original scores and adjustments
- Maintains score validity and bounds

## Example Scenarios

### Scenario 1: Fever Confirmation
```
Question: "Do you have a fever?"
Answer: "Yes, I have a high fever of 102°F"

Results:
- Influenza: 0.700 → 0.800 (+0.100) ✅ Fever is key symptom
- Common Cold: 0.750 → 0.650 (-0.100) ✅ Colds rarely have high fever
- Allergic Rhinitis: 0.650 → 0.650 (0.000) ✅ No fever in allergies
```

### Scenario 2: Muscle Aches Denial
```
Question: "Are you experiencing severe muscle aches?"
Answer: "No, no muscle aches at all"

Results:
- Influenza: 0.700 → 0.600 (-0.100) ✅ Muscle aches are key flu symptom
- Common Cold: 0.750 → 0.750 (0.000) ✅ Colds don't typically have muscle aches
- Migraine: 0.600 → 0.600 (0.000) ✅ Migraines don't cause muscle aches
```

### Scenario 3: Nasal Symptoms Confirmation
```
Question: "Do you have a runny nose or nasal congestion?"
Answer: "Yes, very runny nose and stuffy"

Results:
- Common Cold: 0.750 → 0.850 (+0.100) ✅ Key cold symptoms
- Allergic Rhinitis: 0.650 → 0.750 (+0.100) ✅ Key allergy symptoms
- Influenza: 0.700 → 0.700 (0.000) ✅ Flu can have nasal symptoms but not primary
```

## AI Prompt Structure

The AI receives a structured prompt:

```
You are a medical AI assistant. Based on the patient's answer to a follow-up question, 
adjust the confidence scores for similar diagnoses.

Question asked: [QUESTION]
Patient's answer: [ANSWER]

Current diagnoses and scores:
- Diagnosis 1 (current score: 0.750): [symptoms]
- Diagnosis 2 (current score: 0.700): [symptoms]
...

Instructions:
1. Analyze how the patient's answer affects each diagnosis
2. If the answer supports a diagnosis, increase its score
3. If the answer contradicts a diagnosis, decrease its score
4. If the answer is neutral/irrelevant, keep the score unchanged
5. Provide new scores between 0.0 and 1.0

Respond with ONLY a JSON object:
{"diagnosis_name_1": new_score, "diagnosis_name_2": new_score, ...}
```

## Data Structure

Each adjusted diagnosis includes:

```python
{
    "name": "Influenza",
    "symptoms": "fever, headache, muscle aches, fatigue",
    "confidence_score": 0.800,           # New AI-adjusted score
    "original_score": 0.700,             # Original score before adjustment
    "similarity_adjustment": +0.100,     # Change from this adjustment
    "diagnosis_id": 2
}
```

## Benefits

1. **Intelligent Refinement**: Scores are adjusted based on medical reasoning, not just keywords
2. **Comprehensive Analysis**: All diagnoses are considered for each answer
3. **Cumulative Learning**: Multiple Q&A rounds progressively refine scores
4. **Medical Accuracy**: AI understands symptom relationships and medical context

## Error Handling

- Falls back to original scores if AI fails
- Validates JSON responses from AI
- Ensures scores remain within 0.0-1.0 range
- Logs errors for debugging while maintaining functionality

## Testing

Two test scripts demonstrate the functionality:

1. **`test_similar_diagnosis_adjustment.py`**: Tests individual scenarios
2. **`complete_flow_example.py`**: Shows full diagnosis flow with multiple adjustments

Run tests:
```bash
python test_similar_diagnosis_adjustment.py
python complete_flow_example.py
```

## Performance Considerations

- One AI API call per answer (for all diagnoses at once)
- JSON parsing and validation
- Graceful fallback if AI is unavailable
- Efficient score adjustment without database queries

## Future Enhancements

1. **Batch Optimization**: Process multiple Q&A pairs in single API call
2. **Learning Memory**: Remember common adjustment patterns
3. **Confidence Intervals**: Provide uncertainty estimates
4. **Symptom Weighting**: Different symptoms have different diagnostic importance
