# Medical Diagnosis API - Local Development Setup

This project provides a REST API for medical diagnosis using vector search and AI-powered chat functionality.

## Prerequisites

1. **Docker and Docker Compose** installed on your system
2. **SSH Key**: Place your `aws-ah-s-key2.pem` file in the project root directory
3. **Environment Variables**: Ensure your `.env` file is properly configured

## Quick Start

### 1. Prepare SSH Key
```bash
# Place your SSH key in the project root and set proper permissions
chmod 600 aws-ah-s-key2.pem
```

### 2. Start the Application
```bash
# Build and start all services
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d
```

### 3. Access the Application
- **API Endpoint**: http://localhost:5021
- **Health Check**: http://localhost:5021/api/health

### 4. Stop the Application
```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## Services

### SSH Tunnel Service
- Creates secure tunnel to AWS RDS PostgreSQL database
- Maps remote database to local port 5432
- Automatically handles connection through bastion host

### Diagnosis API Service
- Runs the Flask application on port 5021
- Connects to database through SSH tunnel
- Includes hot-reload for development

## Environment Configuration

The application uses the following environment variables from `.env`:

```env
# Database Configuration
DB_NAME=krushalstage2
DB_USER=krushalstage1
DB_PASS=krushalstage1
DB_HOST=localhost  # Will be overridden to use ssh-tunnel service
DB_PORT=5432

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
EMBEDDER_TYPE=openai
EMBEDDING_MODEL=text-embedding-3-small

# Training Data
TRAINING_DATA_PATH=diseases
```

## Development Workflow

1. **Make code changes** - Files are mounted as volumes for hot-reload
2. **Test API endpoints** - Use curl, Postman, or your frontend
3. **View logs** - `docker-compose logs -f diagnosis-api`
4. **Database access** - Connect to localhost:5432 with your DB credentials

## Troubleshooting

### SSH Connection Issues
```bash
# Check SSH tunnel logs
docker-compose logs ssh-tunnel

# Verify SSH key permissions
ls -la aws-ah-s-key2.pem
```

### Database Connection Issues
```bash
# Test database connectivity
docker-compose exec diagnosis-api python -c "from diagnosis_chat import DiagnosisDatabase; db = DiagnosisDatabase(); print(db.get_diagnosis_count())"
```

### API Issues
```bash
# Check API logs
docker-compose logs diagnosis-api

# Restart specific service
docker-compose restart diagnosis-api
```

## API Endpoints

- `GET /api/health` - Health check endpoint
- `POST /api/chat/start` - Start new chat session
- `POST /api/chat/message` - Send message to chat session
- `GET /api/chat/sessions` - List active sessions

## File Structure

```
.
├── docker-compose.yml          # Docker Compose configuration
├── Dockerfile                  # Application container definition
├── README.md                   # This file
├── aws-ah-s-key2.pem          # SSH private key (not in git)
├── .env                       # Environment variables
├── diagnosis_api.py           # Main API application
├── diagnosis_chat.py          # Chat logic and database classes
├── symptoms_train.py          # Training data processor
├── requirements_api.txt       # Python dependencies
└── diseases/                  # Training data directory
```