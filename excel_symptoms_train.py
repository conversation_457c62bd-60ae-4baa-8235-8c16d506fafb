"""
Excel-based Diagnosis Embedding System
Reads diagnosis data from Excel file and stores in config.diagnoses_embeddings table
Excel columns: diagnosis_name, diagnosis_id, symptoms
"""

import os
import pandas as pd
import psycopg2
import requests
import logging
from typing import List, Dict
from dotenv import load_dotenv
from pathlib import Path
import uuid

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_symptom_embedder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OpenAIEmbedder:
    """Embedder using OpenAI API for text embeddings"""

    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        self.model_name = model or os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

        logger.info(f"Initialized OpenAIEmbedder with model: {self.model_name}")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a list of texts using OpenAI API"""
        logger.info(f"Generating embeddings for {len(texts)} texts using OpenAI API")
        embeddings = []

        try:
            # OpenAI API supports batch processing
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model_name,
                    "input": texts
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get("data", [])

                for item in data:
                    embedding = item.get("embedding", [])
                    if embedding:
                        embeddings.append(embedding)
                    else:
                        logger.warning(f"Empty embedding in response")
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]  # OpenAI default dimension
                        embeddings.append(random_embedding)

                logger.info(f"Generated {len(embeddings)} embeddings successfully")
            else:
                logger.error(f"Error from OpenAI API: {response.status_code} - {response.text}")
                # Generate random embeddings as fallback
                for _ in texts:
                    import random
                    random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]
                    embeddings.append(random_embedding)

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            # Generate random embeddings as fallback
            for _ in texts:
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]
                embeddings.append(random_embedding)

        return embeddings

class ExcelDataProcessor:
    """Process Excel file containing diagnosis data"""

    def __init__(self):
        logger.info("Initialized ExcelDataProcessor")

    def read_excel_file(self, excel_path: str) -> List[Dict]:
        """Read Excel file and return list of diagnosis records"""
        logger.info(f"Reading Excel file: {excel_path}")
        
        try:
            # Read Excel file
            df = pd.read_excel(excel_path)
            
            # Expected columns: diagnosis_name, diagnosis_id, symptoms
            required_columns = ['diagnosis_name', 'diagnosis_id', 'symptoms']
            
            # Check if required columns exist
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Convert to list of dictionaries
            records = []
            for index, row in df.iterrows():
                # Skip rows with missing essential data
                if pd.isna(row['diagnosis_name']) or pd.isna(row['diagnosis_id']) or pd.isna(row['symptoms']):
                    logger.warning(f"Skipping row {index + 1} due to missing data")
                    continue
                
                record = {
                    'diagnosis_name': str(row['diagnosis_name']).strip(),
                    'diagnosis_id': int(row['diagnosis_id']),
                    'symptoms': str(row['symptoms']).strip()
                }
                records.append(record)
            
            logger.info(f"Successfully read {len(records)} valid records from Excel file")
            return records
            
        except Exception as e:
            logger.error(f"Error reading Excel file {excel_path}: {e}")
            return []

class PostgreSQLManager:
    """Manage PostgreSQL database operations for config.diagnoses_embeddings table"""

    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")

        if self.database_url:
            self.db_config = None
            logger.info("Using DATABASE_URL for database connection")
        else:
            self.db_config = {
                "dbname": os.getenv("DB_NAME", "kforce"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASS", "kforce"),
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5444")
            }
            logger.info(f"Using individual DB config: {self.db_config['host']}:{self.db_config['port']}")

    def get_connection(self):
        """Get database connection with retry logic"""
        import time
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                if self.database_url:
                    conn = psycopg2.connect(self.database_url)
                else:
                    conn = psycopg2.connect(**self.db_config)

                # Test the connection
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                    cur.fetchone()

                logger.info(f"Database connection established on attempt {attempt + 1}")
                return conn

            except Exception as e:
                logger.warning(f"Connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    raise

    def insert_diagnosis_embedding(self, diagnosis_id: int, symptoms: str, embedding: List[float]):
        """Insert diagnosis embedding record into config.diagnoses_embeddings table"""

        if not embedding or len(embedding) == 0:
            logger.warning("Empty embedding provided, using random embedding")
            import random
            embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]

        logger.info(f"Inserting diagnosis embedding for diagnosis_id: {diagnosis_id}")

        # First check if record already exists
        check_sql = """
        SELECT id FROM config.diagnoses_embeddings WHERE diagnosis_id = %s
        """

        insert_sql = """
        INSERT INTO config.diagnoses_embeddings (id, diagnosis_id, symptoms, symptoms_embedding)
        VALUES (%s, %s, %s, %s)
        """

        update_sql = """
        UPDATE config.diagnoses_embeddings
        SET symptoms = %s, symptoms_embedding = %s
        WHERE diagnosis_id = %s
        """

        # Generate UUID for the record
        record_id = str(uuid.uuid4())

        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Check if record exists
                    cur.execute(check_sql, (diagnosis_id,))
                    existing_record = cur.fetchone()

                    if existing_record:
                        # Update existing record
                        cur.execute(update_sql, (symptoms, embedding, diagnosis_id))
                        logger.info(f"Updated existing diagnosis embedding for diagnosis_id: {diagnosis_id}")
                    else:
                        # Insert new record
                        cur.execute(insert_sql, (record_id, diagnosis_id, symptoms, embedding))
                        logger.info(f"Inserted new diagnosis embedding for diagnosis_id: {diagnosis_id}")

                    conn.commit()

        except Exception as e:
            logger.error(f"Error inserting/updating record for diagnosis_id {diagnosis_id}: {e}")

    def get_record_count(self) -> int:
        """Get total number of records in config.diagnoses_embeddings table"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT COUNT(*) FROM config.diagnoses_embeddings")
                    return cur.fetchone()[0]
        except Exception as e:
            logger.error(f"Error getting record count: {e}")
            return 0

class ExcelEmbeddingPipeline:
    """Main pipeline for processing Excel diagnosis data and storing embeddings"""

    def __init__(self, embedder_type: str = "openai"):
        logger.info(f"Initializing ExcelEmbeddingPipeline with embedder: {embedder_type}")

        if embedder_type.lower() == "openai":
            self.embedder = OpenAIEmbedder()
        else:
            logger.warning(f"Unknown embedder type: {embedder_type}, defaulting to OpenAI")
            self.embedder = OpenAIEmbedder()

        self.processor = ExcelDataProcessor()
        self.db_manager = PostgreSQLManager()
        logger.info("ExcelEmbeddingPipeline initialized successfully")

    def process_excel_file(self, excel_path: str):
        """Process Excel file and store embeddings"""
        logger.info(f"Processing Excel file: {excel_path}")
        
        if not Path(excel_path).exists():
            logger.error(f"Excel file {excel_path} does not exist")
            return

        # Read Excel data
        records = self.processor.read_excel_file(excel_path)
        if not records:
            logger.error("No valid records found in Excel file")
            return

        logger.info(f"Found {len(records)} records to process")

        # Process records in batches for efficiency
        batch_size = 10
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]
            self.process_batch(batch, i // batch_size + 1, len(records) // batch_size + 1)

        total_records = self.db_manager.get_record_count()
        logger.info(f"Processing complete! Total records in database: {total_records}")

    def process_batch(self, batch: List[Dict], batch_num: int, total_batches: int):
        """Process a batch of records"""
        logger.info(f"Processing batch {batch_num}/{total_batches} with {len(batch)} records")

        # Extract symptoms for embedding generation
        symptoms_texts = [record['symptoms'] for record in batch]
        
        # Generate embeddings for the batch
        embeddings = self.embedder.get_embeddings(symptoms_texts)

        if len(embeddings) != len(batch):
            logger.error(f"Embedding count mismatch: expected {len(batch)}, got {len(embeddings)}")
            return

        # Store each record with its embedding
        for record, embedding in zip(batch, embeddings):
            self.db_manager.insert_diagnosis_embedding(
                diagnosis_id=record['diagnosis_id'],
                symptoms=record['symptoms'],
                embedding=embedding
            )

        logger.info(f"Successfully processed batch {batch_num}/{total_batches}")


if __name__ == "__main__":
    import sys


    pipeline = ExcelEmbeddingPipeline(embedder_type="openai")

        # Process Excel file (using environment variable for path or default)
    excel_file = os.getenv("EXCEL_DATA_PATH", "diagnosis_data.xlsx")

    if not Path(excel_file).exists():
        print(f"Excel file '{excel_file}' not found!")
        sys.exit(1)

    pipeline.process_excel_file(excel_file)
