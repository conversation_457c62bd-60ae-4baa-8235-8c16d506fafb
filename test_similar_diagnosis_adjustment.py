#!/usr/bin/env python3
"""
Test script for similar diagnosis score adjustment functionality
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.v2_routes import ChatSessionV2

def test_similar_diagnosis_adjustment():
    """Test the similar diagnosis score adjustment functionality"""
    
    print("🧪 Testing Similar Diagnosis Score Adjustment...")
    print("=" * 60)
    
    # Create a test session
    session = ChatSessionV2("test-session")
    
    # Add some test symptoms and conversation history
    session.symptoms_history = ["headache, fever, and fatigue"]
    
    # Create some test diagnoses with similar symptoms
    test_diagnoses = [
        {
            "name": "Common Cold",
            "symptoms": "runny nose, nasal congestion, sneezing, mild headache, fatigue",
            "confidence_score": 0.75,
            "diagnosis_id": 1
        },
        {
            "name": "Influenza",
            "symptoms": "fever, headache, muscle aches, fatigue, body aches",
            "confidence_score": 0.70,
            "diagnosis_id": 2
        },
        {
            "name": "Allergic Rhinitis",
            "symptoms": "runny nose, sneezing, nasal congestion, itchy eyes",
            "confidence_score": 0.65,
            "diagnosis_id": 3
        },
        {
            "name": "Migraine",
            "symptoms": "severe headache, nausea, light sensitivity, visual disturbances",
            "confidence_score": 0.60,
            "diagnosis_id": 4
        }
    ]
    
    print("Original diagnoses scores:")
    for diag in test_diagnoses:
        print(f"  - {diag['name']}: {diag['confidence_score']:.3f}")
    
    # Test Scenario 1: Patient confirms fever
    print("\n📋 Scenario 1: Patient confirms having fever")
    question1 = "Do you have a fever?"
    answer1 = "Yes, I have a high fever"
    
    try:
        adjusted_diagnoses_1 = session.adjust_similar_diagnoses_scores(
            test_diagnoses.copy(), question1, answer1
        )
        
        print("Adjusted scores after confirming fever:")
        for diag in adjusted_diagnoses_1:
            original = diag.get('original_score', diag['confidence_score'])
            adjustment = diag.get('similarity_adjustment', 0)
            print(f"  - {diag['name']}: {diag['confidence_score']:.3f} (original: {original:.3f}, adjustment: {adjustment:+.3f})")
        
        print("\n🎯 Expected: Influenza should increase (has fever), Common Cold should decrease (typically no fever)")
        
    except Exception as e:
        print(f"❌ Error in scenario 1: {e}")
    
    # Test Scenario 2: Patient denies muscle aches
    print("\n📋 Scenario 2: Patient denies muscle aches")
    question2 = "Are you experiencing severe muscle aches throughout your body?"
    answer2 = "No, no muscle aches at all"
    
    try:
        adjusted_diagnoses_2 = session.adjust_similar_diagnoses_scores(
            test_diagnoses.copy(), question2, answer2
        )
        
        print("Adjusted scores after denying muscle aches:")
        for diag in adjusted_diagnoses_2:
            original = diag.get('original_score', diag['confidence_score'])
            adjustment = diag.get('similarity_adjustment', 0)
            print(f"  - {diag['name']}: {diag['confidence_score']:.3f} (original: {original:.3f}, adjustment: {adjustment:+.3f})")
        
        print("\n🎯 Expected: Influenza should decrease (muscle aches are key symptom), others should stay similar")
        
    except Exception as e:
        print(f"❌ Error in scenario 2: {e}")
    
    # Test Scenario 3: Patient confirms runny nose
    print("\n📋 Scenario 3: Patient confirms runny nose")
    question3 = "Do you have a runny nose or nasal congestion?"
    answer3 = "Yes, very runny nose and stuffy"
    
    try:
        adjusted_diagnoses_3 = session.adjust_similar_diagnoses_scores(
            test_diagnoses.copy(), question3, answer3
        )
        
        print("Adjusted scores after confirming runny nose:")
        for diag in adjusted_diagnoses_3:
            original = diag.get('original_score', diag['confidence_score'])
            adjustment = diag.get('similarity_adjustment', 0)
            print(f"  - {diag['name']}: {diag['confidence_score']:.3f} (original: {original:.3f}, adjustment: {adjustment:+.3f})")
        
        print("\n🎯 Expected: Common Cold and Allergic Rhinitis should increase (have nasal symptoms)")
        
    except Exception as e:
        print(f"❌ Error in scenario 3: {e}")
    
    print("\n✅ Similar diagnosis adjustment test completed!")
    print("\n💡 This feature helps refine diagnosis confidence based on specific symptom confirmations/denials")

if __name__ == "__main__":
    test_similar_diagnosis_adjustment()
