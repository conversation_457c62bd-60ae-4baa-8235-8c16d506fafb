version: "3.8"

services:
  # ssh-tunnel:
  #   image: alpine:latest
  #   container_name: ssh-tunnel
  #   command: >
  #     sh -c "
  #       apk add --no-cache openssh-client netcat-openbsd &&
  #       cp /ssh-key/aws-ah-s-key2.pem /tmp/key.pem &&
  #       chmod 600 /tmp/key.pem &&
  #       ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ServerAliveInterval=30 -o ServerAliveCountMax=3 -i /tmp/key.pem <EMAIL> -L 0.0.0.0:5432:postgresstage1.cio63gaxksgu.ap-south-1.rds.amazonaws.com:5432 -N
  #     "
  #   volumes:
  #     - ./aws-ah-s-key2.pem:/ssh-key/aws-ah-s-key2.pem:ro
  #   ports:
  #     - "5432:5432"
  #   networks:
  #     - app-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "nc", "-z", "localhost", "5432"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 30s

  diagnosis-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: diagnosis-api
    ports:
      - "5021:5000"
    environment:
      - DB_NAME=krushalstage2
      - DB_USER=krushalstage1
      - DB_PASS=krushalstage1
      # - DB_HOST=ssh-tunnel
      - DB_HOST=postgresstage1.cio63gaxksgu.ap-south-1.rds.amazonaws.com
      - DB_PORT=5432
      # - DATABASE_URL=******************************************************/krushalstage2?ssl=true&sslmode=require
      - DATABASE_URL=postgres://krushalstage1:<EMAIL>:5432/krushalstage2?ssl=true&sslmode=require
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - EMBEDDER_TYPE=openai
      - EMBEDDING_MODEL=text-embedding-3-small
      - TRAINING_DATA_PATH=diseases
      - API_PORT=5000
      - FLASK_DEBUG=true
    volumes:
      - .:/app
      - ./diseases:/app/diseases
    depends_on:
      ssh-tunnel:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge
